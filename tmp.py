#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统计processed_dialogues_openai.json中包含<think>标签的对话数量
"""

import json

def count_dialogues_with_think_tags(json_file_path):
    """
    统计JSON文件中包含<think>标签的对话数量并输出persona_id

    Args:
        json_file_path (str): JSON文件路径

    Returns:
        tuple: (包含<think>标签的对话数量, persona_id列表)
    """
    try:
        # 读取JSON文件
        with open(json_file_path, 'r', encoding='utf-8') as f:
            dialogues = json.load(f)

        print(f"总对话数量: {len(dialogues)}")

        # 统计包含<think>标签的对话数量
        count_with_think = 0
        persona_ids = []

        for i, dialogue in enumerate(dialogues):
            has_think_tag = False

            # 检查对话中的每条消息
            if 'messages' in dialogue:
                for message in dialogue['messages']:
                    # 检查assistant角色的消息是否包含<think>标签
                    if (message.get('role') == 'assistant' and
                        message.get('content') and
                        '<think>' in message.get('content', '')):
                        has_think_tag = True
                        break  # 找到一个就够了，不需要重复统计同一个对话

            if has_think_tag:
                count_with_think += 1
                persona_id = dialogue.get('persona_id', 'N/A')
                persona_ids.append(persona_id)
                print(f"对话 {i+1}: 包含<think>标签, persona_id: {persona_id}")

        return count_with_think, persona_ids

    except FileNotFoundError:
        print(f"错误: 找不到文件 {json_file_path}")
        return 0
    except json.JSONDecodeError:
        print(f"错误: 无法解析JSON文件 {json_file_path}")
        return 0
    except Exception as e:
        print(f"错误: {str(e)}")
        return 0

def main():
    """主函数"""
    json_file_path = "/home/<USER>/DataCleaning/processed_dialogues_openai.json"

    print("开始统计包含<think>标签的对话数量...")
    print("=" * 50)

    count, persona_ids = count_dialogues_with_think_tags(json_file_path)

    print("=" * 50)
    print(f"统计结果: 共有 {count} 个对话包含<think>标签")
    print("\n包含<think>标签的对话的persona_id列表:")
    print("-" * 50)
    for i, persona_id in enumerate(persona_ids, 1):
        print(f"{i:2d}. {persona_id}")

    print(f"\n去重后的persona_id数量: {len(set(persona_ids))}")
    print("去重后的persona_id列表:")
    print("-" * 50)
    unique_persona_ids = sorted(set(persona_ids))
    for i, persona_id in enumerate(unique_persona_ids, 1):
        print(f"{i:2d}. {persona_id}")

if __name__ == "__main__":
    main()
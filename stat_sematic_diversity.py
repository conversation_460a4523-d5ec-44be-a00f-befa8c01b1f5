#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语义多样性分析工具 (Semantic Diversity Analysis Tool)

此脚本用于分析对话数据中的语义多样性，基于CR-POS方法计算语义多样性指标。

主要功能:
    1. 基于CR-POS的语义多样性分析 - 使用词性标注压缩比分析语义多样性
    2. 按角色分组分析 - 分别分析user和assistant角色的语义多样性
    3. 低多样性文本识别 - 识别并输出低于阈值的低多样性文本

用法:
    python stat_sematic_diversity.py input_file [--output_dir OUTPUT_DIR] [--threshold THRESHOLD]

参数:
    input_file            输入的JSON文件路径，包含OpenAI格式的对话数据
    --output_dir, -o      输出文件的目录路径 (默认: ./output)
    --threshold, -t       多样性阈值，高于此值的文本将被标记为低多样性
"""

DEFAULT_DIVERSITY_THRESHOLD = 2.0

import json
import re
import os
import argparse
import gzip
from typing import List, Dict, Tuple, Optional, Literal
import nltk
from nltk import pos_tag
from nltk.tokenize import word_tokenize

# 尝试导入jieba用于中文处理
try:
    import jieba.posseg as pseg
    JIEBA_AVAILABLE = True
except ImportError:
    JIEBA_AVAILABLE = False
    print("警告: jieba未安装，中文文本处理可能不准确。建议安装: pip install jieba")

# 默认多样性阈值 - 基于测试结果调整
# CR-POS值通常在1.0-2.5之间，值越高多样性越低
# 测试显示：高多样性≈1.13，中等多样性≈1.59，低多样性≈1.88，极低多样性≈2.03
# 设置1.8作为阈值，可以准确捕获低多样性和极低多样性的文本


def _ensure_nltk_data():
    """确保NLTK数据已下载"""
    try:
        nltk.data.find('tokenizers/punkt')
    except LookupError:
        nltk.download('punkt', quiet=True)

    try:
        nltk.data.find('taggers/averaged_perceptron_tagger')
    except LookupError:
        nltk.download('averaged_perceptron_tagger', quiet=True)

def _detect_language(text: str) -> str:
    """简单检测文本语言"""
    # 检查是否包含中文字符
    if re.search(r'[\u4e00-\u9fff]', text):
        return 'chinese'
    else:
        return 'english'

def _get_pos_tags_chinese(text: str) -> List[str]:
    """使用jieba进行中文词性标注"""
    if not JIEBA_AVAILABLE:
        # 如果没有jieba，使用简单的字符分割
        return list(text)

    # 使用jieba进行词性标注
    words = pseg.cut(text)
    pos_sequence = []

    for word, flag in words:
        # 保留标点符号和空格
        pos_sequence.append(flag)

    return pos_sequence

def _get_pos_tags_english(text: str) -> List[str]:
    """使用NLTK进行英文词性标注"""
    # 确保NLTK数据已下载
    _ensure_nltk_data()

    try:
        tokens = word_tokenize(text)
        pos_tags = pos_tag(tokens)
        return [tag for _, tag in pos_tags]
    except Exception as e:
        print(f"英文词性标注出错: {e}")
        return []

def compute_cr_pos_diversity(texts: List[str], lang: Optional[Literal['chinese', 'english']] = None) -> Dict[str, float]:
    """
    计算基于词性标注压缩比的语义多样性指标 (CR-POS)。

    Args:
        texts (List[str]): 需要分析的文本列表（支持中英文）
        lang: 语言类型，可选'chinese'或'english'，None时自动检测

    Returns:
        Dict[str, float]: 包含CR-POS多样性指标的字典
    """
    if not texts:
        return {
            "cr_pos_diversity": 0.0,
            "compression_ratio": 1.0,
            "original_size": 0,
            "compressed_size": 0
        }

    # 步骤1: 对每条文本进行词性标注
    pos_sequences = []
    for text in texts:
        try:
            # 检测语言并选择相应的处理方法
            if lang == 'chinese' or lang is None and _detect_language(text) == "chinese":
                pos_sequence = _get_pos_tags_chinese(text)
            else:
                pos_sequence = _get_pos_tags_english(text)

            pos_sequences.append(pos_sequence)
        except Exception as e:
            print(f"警告: 处理文本时出错: {text[:50]}... 错误: {e}")
            # 如果处理失败，使用空序列
            pos_sequences.append([])

    # 步骤2: 将所有POS序列连接成一个长字符串
    # 使用空格分隔不同的POS标签，使用换行符分隔不同的文本
    pos_text = "\n".join([" ".join(seq) for seq in pos_sequences])
    pos_bytes = pos_text.encode('utf-8')

    # 步骤3: 计算原始大小
    original_size = len(pos_bytes)

    # 步骤4: 使用gzip压缩
    compressed_data = gzip.compress(pos_bytes)
    compressed_size = len(compressed_data)

    # 步骤5: 计算压缩比
    compression_ratio = compressed_size / original_size if original_size > 0 else 1.0

    # 步骤6: 计算多样性分数
    diversity_score = 1.0 / compression_ratio if compression_ratio > 0 else 0.0

    return {
        "cr_pos_diversity": diversity_score,
        "compression_ratio": compression_ratio,
        "original_size": original_size,
        "compressed_size": compressed_size
    }

def load_data(file_path):
    """加载JSON数据文件"""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data

def extract_conversations(data):
    """从OpenAI格式的数据中提取对话信息"""
    conversations = []
    skipped_count = 0

    for idx, item in enumerate(data):
        if 'messages' not in item:
            skipped_count += 1
            continue

        conversation = {
            'id': item.get('persona_id', f'conversation_{idx}'),
            'messages': item['messages'],
            'user_messages': [],
            'assistant_messages': []
        }

        for msg in item['messages']:
            if msg.get('role') == 'user':
                content = msg.get('content', '').strip()
                if content:
                    conversation['user_messages'].append(content)
            elif msg.get('role') == 'assistant':
                content = msg.get('content', '').strip()
                if content:
                    conversation['assistant_messages'].append(content)

        user_msg_count = len(conversation['user_messages'])
        assistant_msg_count = len(conversation['assistant_messages'])
        conversation['turns'] = min(user_msg_count, assistant_msg_count)

        if user_msg_count > assistant_msg_count:
            conversation['turns'] += 1

        if conversation['turns'] > 0:
            conversations.append(conversation)
        else:
            skipped_count += 1

    if skipped_count > 0:
        print(f"跳过了 {skipped_count} 条无效对话")

    return conversations

def is_table_content(content):
    """
    检查内容是否包含表格标记

    参数:
        content: 要检查的文本内容

    返回:
        bool: 如果包含表格标记则返回True
    """
    return "|-----" in content or "|----" in content

def analyze_semantic_diversity_by_role(conversations, role_filter, diversity_threshold=DEFAULT_DIVERSITY_THRESHOLD):
    """
    按角色分析语义多样性，识别低多样性文本

    参数:
        conversations: 对话列表
        role_filter: 角色过滤器，'user' 或 'assistant'
        diversity_threshold: 多样性阈值，低于此值的文本将被标记为低多样性

    返回:
        分析结果字典
    """
    # 收集指定角色的非空回复，排除表格内容
    all_messages = []
    message_sources = []  # 记录每个消息的来源（会话ID和消息索引）

    for i, dialogue in enumerate(conversations):
        conv_id = dialogue.get('id', f"conv_{i}")
        messages = dialogue['messages']

        for msg_idx, msg in enumerate(messages):
            # 根据role_filter过滤消息
            if msg['role'] != role_filter:
                continue

            if msg['content'].strip() and len(msg['content'].strip()) > 10:
                content = msg['content'].strip()

                # 排除包含表格标记的内容
                if is_table_content(content):
                    continue

                all_messages.append(content)
                message_sources.append((conv_id, msg_idx, msg['role']))

    print(f"语义多样性分析({role_filter}): 共收集 {len(all_messages)} 条有效消息进行分析")

    if len(all_messages) < 2:
        return {
            "role": role_filter,
            "total_messages": len(all_messages),
            "diversity_metrics": {},
            "low_diversity_texts": []
        }

    # 计算整体多样性
    overall_diversity = compute_cr_pos_diversity(all_messages, lang='chinese')

    # 识别低多样性的文本组合
    low_diversity_results = []

    # 按会话分组分析
    conv_groups = {}
    for i, (conv_id, msg_idx, msg_role) in enumerate(message_sources):
        if conv_id not in conv_groups:
            conv_groups[conv_id] = []
        conv_groups[conv_id].append((all_messages[i], msg_idx))

    # 分析每个会话内的多样性
    for conv_id, messages_with_idx in conv_groups.items():
        if len(messages_with_idx) < 2:
            continue

        messages = [msg for msg, _ in messages_with_idx]
        diversity_metrics = compute_cr_pos_diversity(messages, lang='chinese')

        # 如果CR-POS值高于阈值（多样性低），记录这个会话
        if diversity_metrics['cr_pos_diversity'] >= diversity_threshold:
            low_diversity_results.append({
                "conversation_id": conv_id,
                "role": role_filter,
                "message_count": len(messages),
                "diversity_score": diversity_metrics['cr_pos_diversity'],
                "compression_ratio": diversity_metrics['compression_ratio'],
                "messages": messages,
                "message_indices": [idx for _, idx in messages_with_idx]
            })

    # 按多样性分数排序（最低的在前）
    low_diversity_results.sort(key=lambda x: x['diversity_score'])

    return {
        "role": role_filter,
        "total_messages": len(all_messages),
        "diversity_metrics": overall_diversity,
        "low_diversity_texts": low_diversity_results,
        "low_diversity_count": len(low_diversity_results)
    }

def analyze_semantic_diversity(conversations, output_dir, output_base, diversity_threshold=DEFAULT_DIVERSITY_THRESHOLD):
    """
    执行语义多样性分析并保存结果

    参数:
        conversations: 对话列表
        output_dir: 输出目录
        output_base: 输出文件基础名称
        diversity_threshold: 多样性阈值
    """
    print("正在进行语义多样性分析...")

    # 分析用户消息的语义多样性
    print("  - 分析用户消息语义多样性...")
    user_results = analyze_semantic_diversity_by_role(conversations, 'user', diversity_threshold)

    # 分析助手回复的语义多样性
    print("  - 分析助手回复语义多样性...")
    assistant_results = analyze_semantic_diversity_by_role(conversations, 'assistant', diversity_threshold)

    # 保存用户消息语义多样性结果
    if user_results['low_diversity_texts']:
        user_output_file = os.path.join(output_dir, f"{output_base}_semantic_diversity_user_low.json")
        with open(user_output_file, 'w', encoding='utf-8') as f:
            json.dump(user_results, f, ensure_ascii=False, indent=4)
        print(f"用户消息语义多样性: 共找到 {user_results['low_diversity_count']} 个低多样性会话，已保存到 {user_output_file}")

        # 输出前10个最低多样性的结果摘要
        print("\n用户消息中多样性最低的前10个会话:")
        print("=" * 80)
        for i, item in enumerate(user_results['low_diversity_texts'][:10], 1):
            diversity_score = item.get('diversity_score', 0)
            conv_id = item.get('conversation_id', 'N/A')
            msg_count = item.get('message_count', 0)

            print(f"{i:2d}. 多样性分数: {diversity_score:.4f} | 会话ID: {conv_id} | 消息数: {msg_count}")

            # 显示前2条消息的前50个字符
            messages = item.get('messages', [])
            for j, msg in enumerate(messages[:2], 1):
                msg_preview = msg[:50] + "..." if len(msg) > 50 else msg
                print(f"    消息{j}: {msg_preview}")
            if len(messages) > 2:
                print(f"    ... 还有 {len(messages) - 2} 条消息")
            print()
    else:
        print("用户消息语义多样性: 未找到低于阈值的低多样性会话")

    # 保存助手回复语义多样性结果
    if assistant_results['low_diversity_texts']:
        assistant_output_file = os.path.join(output_dir, f"{output_base}_semantic_diversity_assistant_low.json")
        with open(assistant_output_file, 'w', encoding='utf-8') as f:
            json.dump(assistant_results, f, ensure_ascii=False, indent=4)
        print(f"助手回复语义多样性: 共找到 {assistant_results['low_diversity_count']} 个低多样性会话，已保存到 {assistant_output_file}")

        # 输出前10个最低多样性的结果摘要
        print("\n助手回复中多样性最低的前10个会话:")
        print("=" * 80)
        for i, item in enumerate(assistant_results['low_diversity_texts'][:10], 1):
            diversity_score = item.get('diversity_score', 0)
            conv_id = item.get('conversation_id', 'N/A')
            msg_count = item.get('message_count', 0)

            print(f"{i:2d}. 多样性分数: {diversity_score:.4f} | 会话ID: {conv_id} | 消息数: {msg_count}")

            # 显示前2条消息的前50个字符
            messages = item.get('messages', [])
            for j, msg in enumerate(messages[:2], 1):
                msg_preview = msg[:50] + "..." if len(msg) > 50 else msg
                print(f"    消息{j}: {msg_preview}")
            if len(messages) > 2:
                print(f"    ... 还有 {len(messages) - 2} 条消息")
            print()
    else:
        print("助手回复语义多样性: 未找到低于阈值的低多样性会话")

    # 输出整体统计信息
    print("\n=== 整体语义多样性统计 ===")
    print(f"用户消息整体多样性分数: {user_results['diversity_metrics'].get('cr_pos_diversity', 0):.4f}")
    print(f"助手回复整体多样性分数: {assistant_results['diversity_metrics'].get('cr_pos_diversity', 0):.4f}")
    print(f"多样性阈值: {diversity_threshold}")
    print(f"用户低多样性会话数: {user_results['low_diversity_count']}")
    print(f"助手低多样性会话数: {assistant_results['low_diversity_count']}")

def main():
    parser = argparse.ArgumentParser(description="语义多样性分析工具")
    parser.add_argument("input_file", help="输入的JSON文件路径")
    parser.add_argument("--output_dir", "-o", default="./output", help="输出文件的目录路径")
    parser.add_argument("--threshold", "-t", type=float, default=DEFAULT_DIVERSITY_THRESHOLD,
                       help=f"多样性阈值，CR-POS值高于此值的文本将被标记为低多样性 (默认: {DEFAULT_DIVERSITY_THRESHOLD})")
    args = parser.parse_args()

    os.makedirs(args.output_dir, exist_ok=True)

    print("正在加载数据...")
    data = load_data(args.input_file)
    conversations = extract_conversations(data)

    output_base = os.path.basename(args.input_file).split('.')[0]

    # 执行语义多样性分析
    analyze_semantic_diversity(conversations, args.output_dir, output_base, args.threshold)

    print(f"\n语义多样性分析完成！所有结果已保存到 {args.output_dir} 目录")

if __name__ == "__main__":
    main()
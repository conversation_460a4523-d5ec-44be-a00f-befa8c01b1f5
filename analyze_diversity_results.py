#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析多样性分析结果文件，统计每个文件中的persona_id数量
"""

import json
import os
from collections import defaultdict

def analyze_cr_pos_files(file_path):
    """分析CR-POS语义多样性文件"""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    persona_ids = set()
    role = data.get('role', 'unknown')
    total_messages = data.get('total_messages', 0)
    low_diversity_texts = data.get('low_diversity_texts', [])
    
    for item in low_diversity_texts:
        conversation_id = item.get('conversation_id')
        if conversation_id:
            persona_ids.add(conversation_id)
    
    return {
        'method': 'CR-POS语义多样性',
        'role': role,
        'total_messages': total_messages,
        'unique_persona_ids': len(persona_ids),
        'persona_ids': persona_ids,
        'low_diversity_conversations': len(low_diversity_texts)
    }

def analyze_cosine_similarity_files(file_path):
    """分析余弦相似度文件"""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    persona_ids = set()
    
    for item in data:
        persona1 = item.get('persona1')
        persona2 = item.get('persona2')
        if persona1:
            persona_ids.add(persona1)
        if persona2:
            persona_ids.add(persona2)
    
    # 从文件名推断角色
    filename = os.path.basename(file_path)
    if 'assistant' in filename:
        role = 'assistant'
    elif 'user' in filename:
        role = 'user'
    else:
        role = 'all'
    
    return {
        'method': '余弦相似度 + TF-IDF',
        'role': role,
        'unique_persona_ids': len(persona_ids),
        'persona_ids': persona_ids,
        'similar_pairs': len(data)
    }

def analyze_lcs_files(file_path):
    """分析最长公共子串(LCS)文件"""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    persona_ids = set()
    
    for item in data:
        persona_id = item.get('persona_id')
        if persona_id:
            persona_ids.add(persona_id)
    
    # 从文件名推断角色
    filename = os.path.basename(file_path)
    if 'assistant' in filename:
        role = 'assistant'
    elif 'user' in filename:
        role = 'user'
    else:
        role = 'unknown'
    
    return {
        'method': '最长公共子串(LCS)',
        'role': role,
        'unique_persona_ids': len(persona_ids),
        'persona_ids': persona_ids,
        'duplicate_pairs': len(data)
    }

def main():
    # 定义文件路径
    files_to_analyze = [
        # CR-POS语义多样性文件
        ('output/cr_pos/processed_dialogues_cleaned_semantic_diversity_assistant_low.json', analyze_cr_pos_files),
        ('output/cr_pos/processed_dialogues_cleaned_semantic_diversity_user_low.json', analyze_cr_pos_files),
        
        # 余弦相似度文件
        ('output/lexical/processed_dialogues_cleaned_cosine_similarity_all.json', analyze_cosine_similarity_files),
        ('output/lexical/processed_dialogues_cleaned_cosine_similarity_assistant.json', analyze_cosine_similarity_files),
        ('output/lexical/processed_dialogues_cleaned_cosine_similarity_user.json', analyze_cosine_similarity_files),
        
        # LCS文件
        ('output/lexical/processed_dialogues_cleaned_lcs_assistant.json', analyze_lcs_files),
        ('output/lexical/processed_dialogues_cleaned_lcs_user.json', analyze_lcs_files),
    ]
    
    results = []
    all_persona_ids = set()
    
    print("=== 多样性分析结果统计 ===\n")
    
    for file_path, analyzer_func in files_to_analyze:
        if os.path.exists(file_path):
            try:
                result = analyzer_func(file_path)
                results.append(result)
                all_persona_ids.update(result['persona_ids'])
                
                print(f"文件: {os.path.basename(file_path)}")
                print(f"方法: {result['method']}")
                print(f"角色: {result['role']}")
                print(f"唯一persona_id数量: {result['unique_persona_ids']}")
                
                if 'total_messages' in result:
                    print(f"总消息数: {result['total_messages']}")
                if 'low_diversity_conversations' in result:
                    print(f"低多样性会话数: {result['low_diversity_conversations']}")
                if 'similar_pairs' in result:
                    print(f"相似对数: {result['similar_pairs']}")
                if 'duplicate_pairs' in result:
                    print(f"重复对数: {result['duplicate_pairs']}")
                
                print("-" * 50)
                
            except Exception as e:
                print(f"分析文件 {file_path} 时出错: {e}")
        else:
            print(f"文件不存在: {file_path}")
    
    # 按方法分组统计
    print("\n=== 按方法分组统计 ===\n")
    
    method_stats = defaultdict(lambda: {'assistant': set(), 'user': set(), 'all': set()})
    
    for result in results:
        method = result['method']
        role = result['role']
        persona_ids = result['persona_ids']
        method_stats[method][role].update(persona_ids)
    
    for method, roles in method_stats.items():
        print(f"方法: {method}")
        for role, persona_ids in roles.items():
            if persona_ids:
                print(f"  {role}角色: {len(persona_ids)} 个唯一persona_id")
        
        # 计算该方法涉及的所有persona_id
        all_method_personas = set()
        for persona_ids in roles.values():
            all_method_personas.update(persona_ids)
        print(f"  该方法总计: {len(all_method_personas)} 个唯一persona_id")
        print()
    
    # 交集分析
    print("=== 交集分析 ===\n")
    
    # 提取各方法的persona_id集合
    cr_pos_personas = set()
    cosine_personas = set()
    lcs_personas = set()
    
    for result in results:
        if result['method'] == 'CR-POS语义多样性':
            cr_pos_personas.update(result['persona_ids'])
        elif result['method'] == '余弦相似度 + TF-IDF':
            cosine_personas.update(result['persona_ids'])
        elif result['method'] == '最长公共子串(LCS)':
            lcs_personas.update(result['persona_ids'])
    
    print(f"CR-POS方法识别的persona_id: {len(cr_pos_personas)}")
    print(f"余弦相似度方法识别的persona_id: {len(cosine_personas)}")
    print(f"LCS方法识别的persona_id: {len(lcs_personas)}")
    print()
    
    # 计算交集
    cr_pos_cosine = cr_pos_personas & cosine_personas
    cr_pos_lcs = cr_pos_personas & lcs_personas
    cosine_lcs = cosine_personas & lcs_personas
    all_three = cr_pos_personas & cosine_personas & lcs_personas
    
    print(f"CR-POS ∩ 余弦相似度: {len(cr_pos_cosine)} 个persona_id")
    print(f"CR-POS ∩ LCS: {len(cr_pos_lcs)} 个persona_id")
    print(f"余弦相似度 ∩ LCS: {len(cosine_lcs)} 个persona_id")
    print(f"三种方法都识别的: {len(all_three)} 个persona_id")
    print()
    
    # 独有的persona_id
    cr_pos_only = cr_pos_personas - cosine_personas - lcs_personas
    cosine_only = cosine_personas - cr_pos_personas - lcs_personas
    lcs_only = lcs_personas - cr_pos_personas - cosine_personas
    
    print(f"仅CR-POS识别的: {len(cr_pos_only)} 个persona_id")
    print(f"仅余弦相似度识别的: {len(cosine_only)} 个persona_id")
    print(f"仅LCS识别的: {len(lcs_only)} 个persona_id")
    print()
    
    print(f"所有方法合计识别的唯一persona_id总数: {len(all_persona_ids)}")

if __name__ == "__main__":
    main()

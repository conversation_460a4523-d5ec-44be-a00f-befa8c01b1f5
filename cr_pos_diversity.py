#!/usr/bin/env python3
"""
基于词性标注压缩比的句法多样性计算方法 (CR-POS)

该方法通过以下步骤计算句法多样性：
1. 对每条文本进行词性标注，得到POS标签序列
2. 将所有POS序列连接成一个长字符串
3. 使用gzip压缩该字符串
4. 计算压缩比 = 压缩后大小 / 原始大小
5. 多样性分数 = 1 / 压缩比

压缩比越高，说明句法模板重复越多，多样性越低。

支持中英文文本处理。

Ref: https://amitness.com/posts/diversity-evals/
"""

import gzip
import re
from typing import List, Dict, Tuple, Optional, Literal
import nltk
from nltk import pos_tag
from nltk.tokenize import word_tokenize

# 尝试导入jieba用于中文处理
try:
    import jieba.posseg as pseg
    JIEBA_AVAILABLE = True
except ImportError:
    JIEBA_AVAILABLE = False
    print("警告: jieba未安装，中文文本处理可能不准确。建议安装: pip install jieba")

# 下载必要的NLTK数据
def _ensure_nltk_data():
    """确保NLTK数据已下载"""
    try:
        nltk.data.find('tokenizers/punkt')
    except LookupError:
        nltk.download('punkt', quiet=True)
    
    try:
        nltk.data.find('taggers/averaged_perceptron_tagger')
    except LookupError:
        nltk.download('averaged_perceptron_tagger', quiet=True)


def _detect_language(text: str) -> str:
    """简单检测文本语言"""
    # 检查是否包含中文字符
    if re.search(r'[\u4e00-\u9fff]', text):
        return 'chinese'
    else:
        return 'english'


def _get_pos_tags_chinese(text: str) -> List[str]:
    """使用jieba进行中文词性标注"""
    if not JIEBA_AVAILABLE:
        # 如果没有jieba，使用简单的字符分割
        return list(text)
    
    # 使用jieba进行词性标注
    words = pseg.cut(text)
    pos_sequence = []
    
    for word, flag in words:
        # 保留标点符号和空格
        pos_sequence.append(flag)
    
    return pos_sequence


def _get_pos_tags_english(text: str) -> List[str]:
    """使用NLTK进行英文词性标注"""
    # 确保NLTK数据已下载
    _ensure_nltk_data()
    
    try:
        tokens = word_tokenize(text)
        pos_tags = pos_tag(tokens)
        return [tag for _, tag in pos_tags]
    except Exception as e:
        print(f"英文词性标注出错: {e}")
        return []


def compute_cr_pos_diversity(texts: List[str], lang: Optional[Literal['chinese', 'english']] = None) -> Dict[str, float]:
    """
    计算基于词性标注压缩比的句法多样性指标 (CR-POS)。
    
    Args:
        texts (List[str]): 需要分析的文本列表（支持中英文）
        
    Returns:
        Dict[str, float]: 包含CR-POS多样性指标的字典，包括：
            - cr_pos_diversity: 多样性分数 (0-1之间，越高越多样)
            - compression_ratio: 压缩比 (越小越多样)
            - original_size: 原始POS文本大小
            - compressed_size: 压缩后大小
    """
    if not texts:
        return {
            "cr_pos_diversity": 0.0, 
            "compression_ratio": 1.0,
            "original_size": 0,
            "compressed_size": 0
        }
    
    # 步骤1: 对每条文本进行词性标注
    pos_sequences = []
    for text in texts:
        try:
            # 检测语言并选择相应的处理方法
            if lang == 'chinese' or lang is None and _detect_language(text) == "chinese":
                pos_sequence = _get_pos_tags_chinese(text)
            else:
                pos_sequence = _get_pos_tags_english(text)
            
            pos_sequences.append(pos_sequence)
        except Exception as e:
            print(f"警告: 处理文本时出错: {text[:50]}... 错误: {e}")
            # 如果处理失败，使用空序列
            pos_sequences.append([])
    
    # 步骤2: 将所有POS序列连接成一个长字符串
    # 使用空格分隔不同的POS标签，使用换行符分隔不同的文本
    pos_text = "\n".join([" ".join(seq) for seq in pos_sequences])
    pos_bytes = pos_text.encode('utf-8')
    
    # 步骤3: 计算原始大小
    original_size = len(pos_bytes)
    
    # 步骤4: 使用gzip压缩
    compressed_data = gzip.compress(pos_bytes)
    compressed_size = len(compressed_data)
    
    # 步骤5: 计算压缩比
    compression_ratio = compressed_size / original_size if original_size > 0 else 1.0
    
    # 步骤6: 计算多样性分数
    diversity_score = 1.0 / compression_ratio if compression_ratio > 0 else 0.0
    
    return {
        "cr_pos_diversity": diversity_score,
        "compression_ratio": compression_ratio,
        "original_size": original_size,
        "compressed_size": compressed_size
    }


def analyze_pos_patterns(texts: List[str], lang: Optional[Literal['chinese', 'english']] = None) -> Dict[str, any]:
    """
    分析POS模式，提供更详细的句法分析信息。
    
    Args:
        texts (List[str]): 需要分析的文本列表（支持中英文）
        
    Returns:
        Dict[str, any]: 包含POS模式分析的字典
    """
    
    if not texts:
        return {
            "pos_sequences": [],
            "unique_pos_tags": set(),
            "pos_frequency": {},
            "avg_sequence_length": 0.0
        }
    
    pos_sequences = []
    all_pos_tags = []
    
    for text in texts:
        try:
            # 检测语言并选择相应的处理方法
            if lang == 'chinese' or lang is None and _detect_language(text) == "chinese":
                pos_sequence = _get_pos_tags_chinese(text)
            else:
                pos_sequence = _get_pos_tags_english(text)
            
            pos_sequences.append(pos_sequence)
            all_pos_tags.extend(pos_sequence)
        except Exception as e:
            print(f"警告: 处理文本时出错: {text[:50]}... 错误: {e}")
            pos_sequences.append([])
    
    # 统计POS标签频率
    from collections import Counter
    pos_frequency = Counter(all_pos_tags)
    
    # 计算平均序列长度
    avg_length = sum(len(seq) for seq in pos_sequences) / len(pos_sequences) if pos_sequences else 0.0
    
    return {
        "pos_sequences": pos_sequences,
        "unique_pos_tags": set(all_pos_tags),
        "pos_frequency": dict(pos_frequency),
        "avg_sequence_length": avg_length
    }


def compare_cr_pos_diversity(text_groups: Dict[str, List[str]]) -> Dict[str, Dict[str, float]]:
    """
    比较多个文本组的CR-POS多样性。
    
    Args:
        text_groups (Dict[str, List[str]]): 文本组字典，键为组名，值为文本列表
        
    Returns:
        Dict[str, Dict[str, float]]: 每个组的CR-POS指标
    """
    results = {}
    
    for group_name, texts in text_groups.items():
        results[group_name] = compute_cr_pos_diversity(texts)
    
    return results


def test_cr_pos_diversity():
    """测试CR-POS多样性计算"""
    
    # 测试用例1: 高多样性中文文本（不同的句法结构）
    diverse_texts = [
        "我非常喜欢这部电影。",
        "今天天气很美丽。",
        "她快速地跑向商店。",
        "他们一直在努力工作。",
        "你能帮我解决这个问题吗？",
        "这本书是由一位著名作家写的。",
        "我们明天应该去公园。",
        "他正在大学里学习计算机科学。"
    ]
    
    # 测试用例2: 低多样性中文文本（相似的句法结构）
    repetitive_texts = [
        "我喜欢这部电影。",
        "我喜欢这本书。",
        "我喜欢这首歌。",
        "我喜欢这个游戏。",
        "我喜欢这种食物。",
        "我喜欢这个地方。",
        "我喜欢这个人。",
        "我喜欢这个东西。"
    ]
    
    # 测试用例3: 中等多样性中文文本
    medium_texts = [
        "猫在睡觉。",
        "狗在跑步。",
        "鸟在飞翔。",
        "鱼在游泳。",
        "马在奔驰。",
        "兔子在跳跃。",
        "蛇在爬行。",
        "青蛙在跳跃。"
    ]
    
    print("=== 测试CR-POS多样性计算（中文文本）===\n")
    
    # 计算高多样性文本的指标
    diverse_metrics = compute_cr_pos_diversity(diverse_texts, lang='chinese')
    print("高多样性文本的CR-POS指标:")
    for key, value in diverse_metrics.items():
        print(f"  {key}: {value}")
    
    print("\n" + "="*50 + "\n")
    
    # 计算低多样性文本的指标
    repetitive_metrics = compute_cr_pos_diversity(repetitive_texts, lang='chinese')
    print("低多样性文本的CR-POS指标:")
    for key, value in repetitive_metrics.items():
        print(f"  {key}: {value}")
    
    print("\n" + "="*50 + "\n")
    
    # 计算中等多样性文本的指标
    medium_metrics = compute_cr_pos_diversity(medium_texts, lang='chinese')
    print("中等多样性文本的CR-POS指标:")
    for key, value in medium_metrics.items():
        print(f"  {key}: {value}")
    
    print("\n" + "="*50 + "\n")
    
    # 比较结果
    print("比较结果:")
    print(f"高多样性文本的多样性分数: {diverse_metrics['cr_pos_diversity']:.4f}")
    print(f"中等多样性文本的多样性分数: {medium_metrics['cr_pos_diversity']:.4f}")
    print(f"低多样性文本的多样性分数: {repetitive_metrics['cr_pos_diversity']:.4f}")
    print(f"压缩比差异: 高多样性({diverse_metrics['compression_ratio']:.4f}) vs 中等多样性({medium_metrics['compression_ratio']:.4f}) vs 低多样性({repetitive_metrics['compression_ratio']:.4f})")
    
    # 分析POS模式
    print("\n=== POS模式分析 ===\n")
    
    diverse_patterns = analyze_pos_patterns(diverse_texts, lang='chinese')
    repetitive_patterns = analyze_pos_patterns(repetitive_texts, lang='chinese')
    medium_patterns = analyze_pos_patterns(medium_texts, lang='chinese')
    
    print("高多样性文本的POS模式:")
    print(f"  唯一POS标签数量: {len(diverse_patterns['unique_pos_tags'])}")
    print(f"  平均序列长度: {diverse_patterns['avg_sequence_length']:.2f}")
    print(f"  最常见的POS标签: {dict(list(diverse_patterns['pos_frequency'].items())[:5])}")
    
    print("\n中等多样性文本的POS模式:")
    print(f"  唯一POS标签数量: {len(medium_patterns['unique_pos_tags'])}")
    print(f"  平均序列长度: {medium_patterns['avg_sequence_length']:.2f}")
    print(f"  最常见的POS标签: {dict(list(medium_patterns['pos_frequency'].items())[:5])}")
    
    print("\n低多样性文本的POS模式:")
    print(f"  唯一POS标签数量: {len(repetitive_patterns['unique_pos_tags'])}")
    print(f"  平均序列长度: {repetitive_patterns['avg_sequence_length']:.2f}")
    print(f"  最常见的POS标签: {dict(list(repetitive_patterns['pos_frequency'].items())[:5])}")


if __name__ == "__main__":
    test_cr_pos_diversity() 
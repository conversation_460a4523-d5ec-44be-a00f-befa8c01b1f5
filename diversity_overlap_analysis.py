#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多样性方法重合度详细分析
"""

import json
import os
from collections import defaultdict

def load_and_extract_persona_ids():
    """加载所有文件并提取persona_id"""
    
    # CR-POS方法
    cr_pos_assistant_file = 'output/cr_pos/processed_dialogues_cleaned_semantic_diversity_assistant_low.json'
    cr_pos_user_file = 'output/cr_pos/processed_dialogues_cleaned_semantic_diversity_user_low.json'
    
    # 余弦相似度方法
    cosine_all_file = 'output/lexical/processed_dialogues_cleaned_cosine_similarity_all.json'
    cosine_assistant_file = 'output/lexical/processed_dialogues_cleaned_cosine_similarity_assistant.json'
    cosine_user_file = 'output/lexical/processed_dialogues_cleaned_cosine_similarity_user.json'
    
    # LCS方法
    lcs_assistant_file = 'output/lexical/processed_dialogues_cleaned_lcs_assistant.json'
    lcs_user_file = 'output/lexical/processed_dialogues_cleaned_lcs_user.json'
    
    # 提取CR-POS persona_ids
    cr_pos_personas = set()
    for file_path in [cr_pos_assistant_file, cr_pos_user_file]:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                for item in data.get('low_diversity_texts', []):
                    if 'conversation_id' in item:
                        cr_pos_personas.add(item['conversation_id'])
    
    # 提取余弦相似度persona_ids
    cosine_personas = set()
    for file_path in [cosine_all_file, cosine_assistant_file, cosine_user_file]:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                for item in data:
                    if 'persona1' in item:
                        cosine_personas.add(item['persona1'])
                    if 'persona2' in item:
                        cosine_personas.add(item['persona2'])
    
    # 提取LCS persona_ids
    lcs_personas = set()
    for file_path in [lcs_assistant_file, lcs_user_file]:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                for item in data:
                    if 'persona_id' in item:
                        lcs_personas.add(item['persona_id'])
    
    return cr_pos_personas, cosine_personas, lcs_personas

def calculate_overlap_metrics(set1, set2, name1, name2):
    """计算两个集合的重合度指标"""
    intersection = set1 & set2
    union = set1 | set2
    
    # Jaccard相似度
    jaccard = len(intersection) / len(union) if len(union) > 0 else 0
    
    # 重合比例
    overlap_ratio1 = len(intersection) / len(set1) * 100 if len(set1) > 0 else 0
    overlap_ratio2 = len(intersection) / len(set2) * 100 if len(set2) > 0 else 0
    
    return {
        'intersection_size': len(intersection),
        'jaccard_similarity': jaccard,
        f'{name1}_overlap_ratio': overlap_ratio1,
        f'{name2}_overlap_ratio': overlap_ratio2,
        'intersection_ids': list(intersection)
    }

def main():
    print("=== 多样性方法重合度详细分析 ===\n")
    
    # 加载数据
    cr_pos_personas, cosine_personas, lcs_personas = load_and_extract_persona_ids()
    all_personas = cr_pos_personas | cosine_personas | lcs_personas
    
    print("1. 各方法识别的persona_id数量:")
    print(f"   CR-POS方法: {len(cr_pos_personas)} 个")
    print(f"   余弦相似度 + TF-IDF方法: {len(cosine_personas)} 个")
    print(f"   LCS方法: {len(lcs_personas)} 个")
    print(f"   所有方法合计（去重后）: {len(all_personas)} 个")
    print()
    
    print("2. 各方法占总量的比例:")
    total_count = len(all_personas)
    cr_pos_ratio = len(cr_pos_personas) / total_count * 100
    cosine_ratio = len(cosine_personas) / total_count * 100
    lcs_ratio = len(lcs_personas) / total_count * 100
    
    print(f"   CR-POS方法: {cr_pos_ratio:.1f}% ({len(cr_pos_personas)}/{total_count})")
    print(f"   余弦相似度方法: {cosine_ratio:.1f}% ({len(cosine_personas)}/{total_count})")
    print(f"   LCS方法: {lcs_ratio:.1f}% ({len(lcs_personas)}/{total_count})")
    print()
    
    print("3. 两两重合度分析:")
    
    # CR-POS vs 余弦相似度
    cr_cosine_metrics = calculate_overlap_metrics(cr_pos_personas, cosine_personas, "CR-POS", "余弦相似度")
    print(f"   CR-POS vs 余弦相似度:")
    print(f"     重合数量: {cr_cosine_metrics['intersection_size']}")
    print(f"     Jaccard相似度: {cr_cosine_metrics['jaccard_similarity']:.3f}")
    print(f"     CR-POS的重合比例: {cr_cosine_metrics['CR-POS_overlap_ratio']:.1f}%")
    print(f"     余弦相似度的重合比例: {cr_cosine_metrics['余弦相似度_overlap_ratio']:.1f}%")
    print()
    
    # CR-POS vs LCS
    cr_lcs_metrics = calculate_overlap_metrics(cr_pos_personas, lcs_personas, "CR-POS", "LCS")
    print(f"   CR-POS vs LCS:")
    print(f"     重合数量: {cr_lcs_metrics['intersection_size']}")
    print(f"     Jaccard相似度: {cr_lcs_metrics['jaccard_similarity']:.3f}")
    print(f"     CR-POS的重合比例: {cr_lcs_metrics['CR-POS_overlap_ratio']:.1f}%")
    print(f"     LCS的重合比例: {cr_lcs_metrics['LCS_overlap_ratio']:.1f}%")
    print()
    
    # 余弦相似度 vs LCS
    cosine_lcs_metrics = calculate_overlap_metrics(cosine_personas, lcs_personas, "余弦相似度", "LCS")
    print(f"   余弦相似度 vs LCS:")
    print(f"     重合数量: {cosine_lcs_metrics['intersection_size']}")
    print(f"     Jaccard相似度: {cosine_lcs_metrics['jaccard_similarity']:.3f}")
    print(f"     余弦相似度的重合比例: {cosine_lcs_metrics['余弦相似度_overlap_ratio']:.1f}%")
    print(f"     LCS的重合比例: {cosine_lcs_metrics['LCS_overlap_ratio']:.1f}%")
    print()
    
    print("4. 三种方法的交集和独有分析:")
    
    # 三种方法的交集
    all_three_intersection = cr_pos_personas & cosine_personas & lcs_personas
    print(f"   三种方法都识别的persona_id: {len(all_three_intersection)} 个")
    
    # 各方法中三方交集的占比
    cr_pos_three_ratio = len(all_three_intersection) / len(cr_pos_personas) * 100 if len(cr_pos_personas) > 0 else 0
    cosine_three_ratio = len(all_three_intersection) / len(cosine_personas) * 100 if len(cosine_personas) > 0 else 0
    lcs_three_ratio = len(all_three_intersection) / len(lcs_personas) * 100 if len(lcs_personas) > 0 else 0
    
    print(f"   三方交集占各方法的比例:")
    print(f"     占CR-POS的比例: {cr_pos_three_ratio:.1f}%")
    print(f"     占余弦相似度的比例: {cosine_three_ratio:.1f}%")
    print(f"     占LCS的比例: {lcs_three_ratio:.1f}%")
    print()
    
    # 独有的persona_id
    cr_pos_only = cr_pos_personas - cosine_personas - lcs_personas
    cosine_only = cosine_personas - cr_pos_personas - lcs_personas
    lcs_only = lcs_personas - cr_pos_personas - cosine_personas
    
    print(f"   各方法独有的persona_id:")
    print(f"     仅CR-POS识别: {len(cr_pos_only)} 个 ({len(cr_pos_only)/len(cr_pos_personas)*100:.1f}% of CR-POS)")
    print(f"     仅余弦相似度识别: {len(cosine_only)} 个 ({len(cosine_only)/len(cosine_personas)*100:.1f}% of 余弦相似度)")
    print(f"     仅LCS识别: {len(lcs_only)} 个 ({len(lcs_only)/len(lcs_personas)*100:.1f}% of LCS)")
    print()
    
    # 两两交集但不在第三个方法中的
    cr_cosine_not_lcs = (cr_pos_personas & cosine_personas) - lcs_personas
    cr_lcs_not_cosine = (cr_pos_personas & lcs_personas) - cosine_personas
    cosine_lcs_not_cr = (cosine_personas & lcs_personas) - cr_pos_personas
    
    print(f"   两两交集但不在第三个方法中:")
    print(f"     CR-POS ∩ 余弦相似度 - LCS: {len(cr_cosine_not_lcs)} 个")
    print(f"     CR-POS ∩ LCS - 余弦相似度: {len(cr_lcs_not_cosine)} 个")
    print(f"     余弦相似度 ∩ LCS - CR-POS: {len(cosine_lcs_not_cr)} 个")
    print()
    
    print("5. 总结:")
    print(f"   - CR-POS方法识别能力最强，识别了 {len(cr_pos_personas)} 个低多样性persona")
    print(f"   - LCS方法次之，识别了 {len(lcs_personas)} 个")
    print(f"   - 余弦相似度方法识别了 {len(cosine_personas)} 个")
    print(f"   - 三种方法的重合度较高，特别是CR-POS与LCS的重合度达到 {cr_lcs_metrics['jaccard_similarity']:.1%}")
    print(f"   - 三种方法都识别的'核心'低多样性persona有 {len(all_three_intersection)} 个")
    
    # 保存详细结果
    results = {
        'summary': {
            'cr_pos_count': len(cr_pos_personas),
            'cosine_count': len(cosine_personas),
            'lcs_count': len(lcs_personas),
            'total_unique_count': len(all_personas)
        },
        'overlap_analysis': {
            'cr_pos_vs_cosine': cr_cosine_metrics,
            'cr_pos_vs_lcs': cr_lcs_metrics,
            'cosine_vs_lcs': cosine_lcs_metrics
        },
        'intersection_analysis': {
            'all_three_intersection': len(all_three_intersection),
            'cr_pos_only': len(cr_pos_only),
            'cosine_only': len(cosine_only),
            'lcs_only': len(lcs_only)
        },
        'persona_ids': {
            'cr_pos': list(cr_pos_personas),
            'cosine': list(cosine_personas),
            'lcs': list(lcs_personas),
            'all_three_intersection': list(all_three_intersection)
        }
    }
    
    # 保存到文件（不包含具体的persona_id列表，太大了）
    results_summary = {k: v for k, v in results.items() if k != 'persona_ids'}
    
    with open('diversity_overlap_analysis.json', 'w', encoding='utf-8') as f:
        json.dump(results_summary, f, ensure_ascii=False, indent=2)
    
    print(f"\n详细分析结果已保存到: diversity_overlap_analysis.json")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
删除包含<think>标签的assistant回复及其后续所有内容的脚本
"""

import json
import sys
from pathlib import Path

def process_dialogues(input_file, output_file):
    """
    处理对话数据，删除包含<think>标签的assistant回复及其后续内容

    Args:
        input_file: 输入JSON文件路径
        output_file: 输出JSON文件路径
    """
    print(f"正在读取文件: {input_file}")

    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        print(f"读取文件失败: {e}")
        return

    print(f"原始数据包含 {len(data)} 个对话")

    processed_data = []
    truncated_personas = []

    for dialogue in data:
        persona_id = dialogue.get('persona_id', 'unknown')
        messages = dialogue.get('messages', [])

        # 查找第一个包含<think>的assistant消息
        truncate_index = None
        for i, message in enumerate(messages):
            if (message.get('role') == 'assistant' and
                isinstance(message.get('content'), str) and
                '<think>' in message.get('content', '')):
                truncate_index = i
                break

        if truncate_index is not None:
            # 截断对话，保留截断点之前的消息
            truncated_messages = messages[:truncate_index]

            # 如果截断后还有消息，则保留这个对话
            if truncated_messages:
                processed_dialogue = {
                    'persona_id': persona_id,
                    'messages': truncated_messages
                }
                processed_data.append(processed_dialogue)

            # 记录被截断的persona_id
            truncated_personas.append(persona_id)
            print(f"截断 persona_id: {persona_id} (在第 {truncate_index + 1} 条消息处)")
        else:
            # 没有找到<think>标签，保留完整对话
            processed_data.append(dialogue)

    print(f"\n处理完成:")
    print(f"- 原始对话数: {len(data)}")
    print(f"- 处理后对话数: {len(processed_data)}")
    print(f"- 被截断的对话数: {len(truncated_personas)}")

    # 保存处理后的数据
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(processed_data, f, ensure_ascii=False, indent=2)
        print(f"\n处理后的数据已保存到: {output_file}")
    except Exception as e:
        print(f"保存文件失败: {e}")
        return

    # 打印被截断的persona_id列表
    if truncated_personas:
        print(f"\n被截断的 persona_id 列表:")
        for persona_id in truncated_personas:
            print(f"  - {persona_id}")

def main():
    # 默认输入和输出文件
    input_file = "/home/<USER>/DataCleaning/processed_dialogues_thinking.json"
    output_file = "/home/<USER>/DataCleaning/processed_dialogues_cleaned.json"

    # 检查输入文件是否存在
    if not Path(input_file).exists():
        print(f"错误: 输入文件不存在: {input_file}")
        sys.exit(1)

    # 处理对话数据
    process_dialogues(input_file, output_file)

if __name__ == "__main__":
    main()
#!/usr/bin/env python3
"""
脚本用于从processed_dialogues_openai_cosine_similarity_results.json文件中
提取所有的source1和source2字段，并输出去重的persona_id列表
"""

import json
import sys
from pathlib import Path


def extract_unique_persona_ids(json_file_path):
    """
    从JSON文件中提取所有唯一的persona_id
    
    Args:
        json_file_path (str): JSON文件路径
        
    Returns:
        set: 去重后的persona_id集合
    """
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        persona_ids = set()
        
        # 遍历所有记录，收集source1和source2
        for record in data:
            if 'persona1' in record and record['persona1']:
                persona_ids.add(record['persona1'])
            if 'persona2' in record and record['persona2']:
                persona_ids.add(record['persona2'])
        
        return persona_ids
    
    except FileNotFoundError:
        print(f"错误: 找不到文件 {json_file_path}")
        return set()
    except json.JSONDecodeError as e:
        print(f"错误: JSON解析失败 - {e}")
        return set()
    except Exception as e:
        print(f"错误: {e}")
        return set()


def main():
    # 默认文件路径
    default_file = "output/lexical/processed_dialogues_cleaned_cosine_similarity_all.json"
    
    # 如果命令行提供了文件路径，使用命令行参数
    if len(sys.argv) > 1:
        json_file = sys.argv[1]
    else:
        json_file = default_file
    
    # 检查文件是否存在
    if not Path(json_file).exists():
        print(f"错误: 文件 {json_file} 不存在")
        sys.exit(1)
    
    print(f"正在处理文件: {json_file}")
    
    # 提取唯一的persona_id
    unique_persona_ids = extract_unique_persona_ids(json_file)
    
    if not unique_persona_ids:
        print("没有找到任何persona_id")
        return
    
    # 输出结果
    print(f"\n找到 {len(unique_persona_ids)} 个唯一的persona_id:")
    print("=" * 50)
    
    # 按字母顺序排序输出
    sorted_ids = sorted(unique_persona_ids)
    for i, persona_id in enumerate(sorted_ids, 1):
        print(f"{i:3d}. {persona_id}")
    
    # 保存到文件
    output_file = "unique_persona_ids.txt"
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"唯一的persona_id列表 (共{len(unique_persona_ids)}个):\n")
            f.write("=" * 50 + "\n")
            for i, persona_id in enumerate(sorted_ids, 1):
                f.write(f"{i:3d}. {persona_id}\n")
        
        print(f"\n结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"保存文件时出错: {e}")


if __name__ == "__main__":
    main()
